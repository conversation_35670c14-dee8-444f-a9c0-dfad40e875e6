<?php

namespace App\Services;

use App\Line;
use App\User;
use App\SalesTypes;
use App\LineProduct;
use App\SalesSetting;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\LineDivisionUser;
use Carbon\CarbonPeriod;
use Illuminate\Support\Carbon;
use App\Services\Enums\Ceiling;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Models\Distributors\DistributorLine;
use Illuminate\Support\Facades\Log;

class AchievementService
{

    private int $cacheTimeout = 60;
    private int $perDivOrUserFilter;
    private string $view;
    private Carbon $from;
    private Carbon $to;
    private bool $isChecked;
    private array $fromMonth;
    private string $fromYear;
    private array $years = [];
    private array $months = [];
    private CarbonPeriod $period;
    private mixed $targetSetting;
    private mixed $salesPerDistributor;
    private int $divisionType;
    private array $lineIds;
    /**
     * @var User|User[]|\Illuminate\Contracts\Auth\Authenticatable|\LaravelIdea\Helper\App\_IH_User_C|null
     */
    private User $authUser;
    private array $divisionIds;
    private array $userIds;
    private array $filters;
    private mixed $type;
    private array $productIds;
    private mixed $mappingType;

    public function getLines($from, $to): array
    {
        /**@var User $user */
        $user = Auth::user();

        $lines = Cache::remember('user_lines_' . $user->id, $this->cacheTimeout, function () use ($user, $from, $to) {
            return $user->userLines($from, $to);
        });
        $mappingTypes = Cache::remember('mapping_types', $this->cacheTimeout, function () {
            return SalesTypes::get();
        });
        return compact('lines', 'mappingTypes');
    }

    public function getLineData(array $lineIds, Carbon $from = null, Carbon $to = null)
    {
        /**@var User $user */
        $user = Auth::user();

        $cacheKey = 'line_data_' . implode('_', $lineIds) . '_' . $user->id;
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($user, $lineIds, $from, $to) {
            $lines = Line::whereIn('id', $lineIds)->get();

            $divisions = $lines->flatMap(fn($line) => $user->userDivisions($line))->unique('id');
            $users = $user->belowUsersOfAllLinesWithPositions($lines, 'Active', $from, $to);

            $distributors = $this->getDistributors($lineIds, $from, $to);
            $products = $this->getProducts($lineIds, $from, $to);

            return compact('users', 'divisions', 'products', 'distributors');
        });
    }

    private function getDistributors($lineIds, $from, $to)
    {
        return Cache::remember(
            'load_distributors_in_lines:' . implode(',', $lineIds),
            $this->cacheTimeout,
            fn() => DistributorLine::whereIntegerInRaw('line_id', $lineIds)
                ->where('from_date', '<=', $from ?? (string)Carbon::now())
                ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', $to->toDateString() ?? (string)Carbon::now()))
                ->with('distributor')
                ->get()
                ->pluck('distributor')
        );
    }

    private function getProducts($lineIds, $from, $to)
    {
        return LineProduct::whereIntegerInRaw('line_id', $lineIds)
            ->where('from_date', '<=', $from ?? (string)Carbon::now())
            ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', $to->toDateString() ?? (string)Carbon::now()))
            ->with('product')->get()->pluck('product')->unique('id')->values();
    }

    public function getProductData(array $lines, $typeId, $from, $to)
    {
        $cacheKey = 'product_data_' . implode('_', $lines) . '_' . $typeId;


        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($lines, $typeId, $from, $to) {
            $query = LineProduct::whereIntegerInRaw('line_id', $lines)
                ->where(function ($query) use ($from, $to) {
                    $query->where(function ($subQuery) use ($from, $to) {
                        $subQuery->whereNull('line_products.to_date') // Active records
                            ->orWhereBetween('line_products.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                            ->orWhere('line_products.to_date', '>=', $to->toDateString()); // Ends within range
                    })
                        ->where(function ($subQuery) use ($from, $to) {
                            $subQuery->where('line_products.from_date', '<=', $from->toDateString()) // Starts before range
                                ->orWhereBetween('line_products.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                        });
                });
            return match ($typeId) {
                1 => $query->whereHas('product', fn($q) => $q->where('products.is_hidden', 0))
                    ->with('product')
                    ->get()
                    ->pluck('product'),
                2 => $query->whereHas('product', fn($q) => $q->where('products.is_hidden', 0))
                    ->with('product.brands')
                    ->get()
                    ->pluck('product.brands')
                    ->collapse()
                    ->unique('id')
                    ->values(),
                3 => $query->whereHas('product', fn($q) => $q->where('products.is_hidden', 1))
                    ->with('product')
                    ->get()
                    ->pluck('product'),
                default => $query
                    ->with('product')
                    ->get()
                    ->pluck('product')
            };
        });
    }

    public function initializeFilter(array $filters): void
    {
        $this->from = Carbon::parse($filters['fromDate'])->startOfDay();
        $this->to = Carbon::parse($filters['toDate'])->endOfDay();
        $this->fromMonth = [(clone $this->from)->format('m'), (clone $this->to)->format('m')];
        $this->fromYear = (clone $this->from)->format('Y');

        $this->lineIds = $filters['lines'];
        $this->perDivOrUserFilter = $filters['filter'];
        $this->view = $filters['view'];
        $this->isChecked = $filters['checked'];
        $this->period = CarbonPeriod::create($this->from, '1 month', $this->to);

        foreach ($this->period as $date) {
            $this->months[] = $date->format("m");
            $this->years[] = $date->format("Y");
        }

        $this->type = $filters['type'];
        $this->productIds = $filters['products'];

        $this->divisionIds = $filters['divisions'];
        $this->userIds = $filters['users'];

        $this->mappingType = $filters['mappingType'];

        $this->filters = $filters;
    }

    public function initSettings(): void
    {
        $this->targetSetting = Cache::remember('target_setting', $this->cacheTimeout, function () {
            return SalesSetting::where('key', 'target_level')->value('value');
        });

        $this->salesPerDistributor = Cache::remember('sales_per_distributor', $this->cacheTimeout, function () {
            return SalesSetting::where('key', 'mapping_with_distributor')->value('value') == 'Yes';
        });

        $this->divisionType = Cache::remember('division_type', $this->cacheTimeout, function () {
            return DivisionType::where('last_level', 1)->first()->id;
        });
    }

    public function filter(array $saleFilter): array
    {
        $this->authUser = array_key_exists('user_id', $saleFilter) ? User::find($saleFilter['user_id']) : auth()->user();
        $this->initializeFilter($saleFilter);
        $this->initSettings();


        $fields = $this->getFields();


        $data = $this->processLines();

        return [
            'data' => $this->responseData($data)->values(),
            'fields' => $fields,
        ];
    }

    private function processLines(): Collection
    {
        $data = new Collection([]);
        $lines = Line::whereIn('id', $this->lineIds)->with([
            'divisions',
            'users',
            'distributors' => function ($query) {
                $query->where(
                    fn($q) => $q
                        ->whereNull("line_distributors.to_date")
                        ->orWhere("line_distributors.to_date", ">=", now())
                );
            }
        ])->get();
        foreach ($lines as $line) {
            $filtered = $this->getFilteredObjects($line);
            $products = $this->getFilteredProducts($line);

            $data = $data->merge($this->processFilteredObjects($filtered, $line, $products));
        }
        return $data;
    }

    private function getFilteredObjects($line)
    {
        $divisions = collect([]);
        if ($this->perDivOrUserFilter === 1) {
            $divisions = $line->divisions($this->from, $this->to)
                ->when(!empty($this->divisionIds), fn($q) => $q->whereIn("line_divisions.id", $this->divisionIds))->get();
        }
        if ($this->perDivOrUserFilter === 2 && empty($this->userIds)) {
            $divisions = $line->divisions($this->from, $this->to)->get();
        }
        if ($this->perDivOrUserFilter === 2 && !empty($this->userIds)) {
            $divisions = LineDivisionUser::where('line_id', $line->id)->whereIntegerInRaw('user_id', $this->userIds)
                ->where('from_date', '<=', $this->from ?? (string)Carbon::now())
                ->where(fn($q) => $q->where('to_date', '>=', Carbon::parse($this->to)->toDateString() ?? (string)Carbon::now())
                    ->orWhere('to_date', null))->with('linedivision')->get()->pluck('linedivision')->unique('id')->values();
            $this->filters['divisions'] = $divisions->pluck('id')->toArray();
        }
        return $this->authUser->filterDivisions($line, $divisions, $this->filters, $this->from, $this->to);
    }

    private function getFilteredProducts(Line $line)
    {

        $cacheKey = "filtered_products_{$line->id}_{$this->type}_{$this->from}_{$this->to}_" . implode('_', $this->productIds ?? []);
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($line) {
            $productsQuery = $line->products($this->from, $this->to);
            return match ($this->type) {
                1 => $productsQuery->where('products.is_hidden', 0)
                    ->when(!empty($this->productIds), fn($q) => $q->whereIntegerInRaw("products.id", $this->productIds))->with('brands')
                    ->get()->unique('id')->values(),
                2 => $productsQuery->where('products.is_hidden', 0)
                    ->when(!empty($this->productIds), fn($q) => $q->whereHas('brands', fn($query) => $query->whereIntegerInRaw("brands.id", $this->productIds)))
                    ->get()->unique('id')->values(),
                default => $productsQuery
                    // ->where('products.is_hidden', 1)
                    ->when(!empty($this->productIds), fn($q) => $q->whereIntegerInRaw("products.id", $this->productIds))
                    ->get()->unique('id')->values(),
            };
        });
    }

    private function processFilteredObjects($filtered, $line, $products): Collection
    {
        $data = new Collection([]);
        $chunkedFiltered = $filtered->chunk(100);
        foreach ($chunkedFiltered as $chunk) {
            $chunkData = $chunk->map(
                function ($object)
                use ($line, $products) {
                    if ($this->view == 'Sales') {
                        return $this->salesView($object, $line, $products);
                    } else {
                        return $this->monthView($object, $line, $products);
                    }
                }
            )->collapse();

            $data = $data->merge($chunkData);
        }

        return $data;
    }

    private function getCachedSalesData($key, $callback)
    {
        return Cache::remember($key, $this->cacheTimeout, $callback);
    }

    private function getCachedTargetsData($key, $callback)
    {
        return Cache::remember($key, $this->cacheTimeout, $callback);
    }

    private function sales($divisions, $products, $line, $brick = null)
    {
        $cacheKey = "sales_data_" . md5(serialize(func_get_args()));
        return $this->getCachedSalesData($cacheKey, function () use ($divisions, $products, $line, $brick) {
            $productPrice = getAvgProductPriceQuery();

            $query = DB::table("sales")
                ->select([
                    "sales.product_id",
                    DB::raw('DATE_FORMAT(crm_sales.date, "%Y-%m") as date'),
                    DB::raw("SUM(crm_sales_details.quantity) as quantity"),
                    DB::raw("SUM(crm_sales_details.value) as value"),
                    DB::raw(
                        "SUM(
                            CASE
                                WHEN crm_sales_details.value = 0 THEN $productPrice * crm_sales_details.quantity
                                ELSE crm_sales_details.value
                            END
                        ) as sales_value
                        "
                    )
                ])
                ->selectRaw("($productPrice) as debug_price")
                ->leftJoin("mapping_sale", "sales.id", "=", "mapping_sale.sale_id")
                ->join("mappings", function ($q) use ($line) {
                    $q->on("mapping_sale.mapping_id", "=", "mappings.id")
                        ->where(function ($q) use ($line) {
                            $q->whereIntegerInRaw("mappings.line_id", [$line->id])->orWhereNull(
                                "mappings.line_id"
                            );
                        });
                    if ($this->mappingType) {
                        $q->where('mappings.mapping_type_id', $this->mappingType);
                    }
                })
                ->join("sales_details", "sales.id", "=", "sales_details.sale_id")
                ->whereIn("sales.ceiling", [Ceiling::DISTRIBUTED, Ceiling::BELOW])
                ->whereIntegerInRaw("sales.product_id", $products->pluck("id")->toArray())
                ->whereIntegerInRaw("sales_details.div_id", $divisions)
                ->whereBetween("sales.date", [
                    $this->from->format("Y-m-d"),
                    $this->to->format("Y-m-d")
                ])
                ->groupBy(["sales.product_id", DB::raw('DATE_FORMAT(crm_sales.date, "%Y-%m")')]);

            if ($this->isChecked) {
                $query->where('sales_details.brick_id', $brick?->id);
            }

            return $query->get()->groupBy("product_id");
        });
    }

    public function getProductDataPerProduct(Collection $collection, $product): Collection
    {
        $productExists = $collection->has($product->id);
        return $productExists ? $collection->get($product->id) : collect();
    }

    public function getProductDataPerDate(Collection $collection, $date)
    {
        return $collection->where("date", $date)->first();
    }

    public function getProductTargetsSales(Collection $sales, Collection $targets, $product, $date = null): array
    {
        $salesCollection = $this->getProductDataPerProduct($sales, $product);
        $targetsCollection = $this->getProductDataPerProduct($targets, $product);

        if (is_null($date)) {
            $salesUnit = $salesCollection->sum("quantity");
            //            $salesValue = $salesCollection->sum("value");
            $salesValue = $salesCollection->sum("sales_value");

            $targetsUnit = $targetsCollection->sum("target");
            $targetsValue = $targetsCollection->sum("value");
            $targetsValue = $targetsValue != 0 ? $targetsValue : $targetsCollection->sum("target_value");


            return [
                ['salesUnit' => round($salesUnit, 0), 'salesValue' => round($salesValue, 2)],
                ['targetUnit' => round($targetsUnit, 0), 'targetValue' => round($targetsValue, 2)]
            ];
        }

        $date = $date->format('Y-m');
        $sale = $this->getProductDataPerDate($salesCollection, $date);


        $salesUnit = $sale ? $sale->quantity : 0;

        if ($sale) {
            $salesValue = $sale->sales_value;
        } else {
            $salesValue = 0;
        }

        $target = $this->getProductDataPerDate($targetsCollection, $date);
        $targetsUnit = $target ? $target->target : 0;
        if ($target) {
            $targetsValue = $target->value != 0 ? $target->value : $target->target_value;
        } else {
            $targetsValue = 0;
        }
        // $targetsValue = $target ? $target->target_value : 0;

        return [
            ['salesUnit' => round($salesUnit, 0), 'salesValue' => round($salesValue, 2)],
            ['targetUnit' => round($targetsUnit, 0), 'targetValue' => round($targetsValue, 2)]
        ];
    }


    private function getSalesPerDate($sale, $date)
    {
        $date = Carbon::parse($date);

        return $sale->where('date', $date->toDateString())->first();
    }

    private function targets($divisions, $products, $brick = null)
    {
        $cacheKey = "targets_data_" . md5(serialize(func_get_args()));
        return $this->getCachedTargetsData($cacheKey, function () use ($divisions, $products, $brick) {
            $query = DB::table('target_details')->select([
                "target_details.product_id",
                DB::raw('DATE_FORMAT(crm_target_details.date, "%Y-%m") as date'),
                DB::raw("sum(crm_target_details.target) as target"),
                DB::raw("SUM(crm_target_details.value) as value"),
            ])
                ->selectRaw(
                    'sum((
                            SELECT avg_price
                                FROM crm_product_prices
                                WHERE crm_product_prices.product_id = crm_target_details.product_id
                                AND crm_product_prices.deleted_at IS NULL
                                AND crm_product_prices.from_date <= crm_target_details.date
                                AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_target_details.date)
                                LIMIT 1
                            ) * crm_target_details.target) as target_value'
                )
                ->whereIn("target_details.product_id", $products->pluck("id")->toArray())
                ->whereIntegerInRaw("div_id", $divisions)
                ->whereNull("target_details.deleted_at")
                ->whereIn(DB::raw("DATE_FORMAT(crm_target_details.date,'%m')"), $this->months)
                ->whereIn(DB::raw("YEAR(crm_target_details.date)"), $this->years);
            // if ($this->isChecked) {
            //     $query->where('target_details.brick_id', $brick->id);
            // }
            return $query->groupBy([
                'target_details.product_id',
                "target_details.date"
            ])->get()->groupBy("product_id");
        });
    }

    public function responseData($data)
    {
        if ($this->perDivOrUserFilter == 2)
            return $data->unique(function ($item) {
                return [$item['id'] . $item['line'] . $item['employee'] . $item['product']];
            });
        else {
            if (!$this->isChecked) {
                return $data->unique(function ($item) {
                    return [$item['id'] . $item['line'] . $item['division'] . $item['product']];
                });
            } else {
                return $data->unique(function ($item) {
                    return [$item['id'] . $item['line'] . $item['brick'] . $item['product']];
                });
            }
        }
    }

    private function monthView($object, $line, $products): Collection
    {
        $belowDivisions = $this->getBelowDivisions($object, $line);
        $result = collect([]);

        $productData = $this->getProductDataProcessing($products, $line, $object, $belowDivisions);


        $result = $result->merge($productData);

        $total = $this->calculateTotal($result, $object, $line);
        $result->push($total);

        return $result;
    }

    private function salesView($object, $line, $products): Collection
    {
        if ($this->isChecked) {
            return $this->salesViewWithBricks($object, $line, $products);
        } else {
            $belowDivisions = $this->getBelowDivisions($object, $line);
            return $this->getProductDataProcessingInSalesView($products, $line, $object, $belowDivisions);
        }
    }

    public function getFields()
    {
        $fields = [];
        if ($this->perDivOrUserFilter == 1) {
            if ($this->isChecked) {
                $fields = collect(["line", "division", "brick", "employee", "emp_code", "product", "brand"]);
            } else {
                $fields = collect(["line", "division", "employee", "emp_code", "product"]);
            }
        } else {
            $fields = collect(["line", "division", "employee", "emp_code", "product", "brand"]);
        }
        if ($this->view == 'Sales') {
            $fields = $fields->merge(["sales_unit", "sales_value", "target_unit", "target_value", "achievement_unit", "achievement_value"]);
        } else {
            foreach ($this->period as $date) {
                $fields = $fields->merge($date->format('M') . ' SU');
                $fields = $fields->merge($date->format('M') . ' SV');
                $fields = $fields->merge($date->format('M') . ' TU');
                $fields = $fields->merge($date->format('M') . ' TV');
                $fields = $fields->merge($date->format('M') . ' AU');
                $fields = $fields->merge($date->format('M') . ' AV');
            }
            $fields = $fields->merge('Tot SU');
            $fields = $fields->merge('Tot SV');
            $fields = $fields->merge('Tot TU');
            $fields = $fields->merge('Tot TV');
            $fields = $fields->merge('Ach U');
            $fields = $fields->merge('Ach V');
        }
        return $fields;
    }

    private function salesViewWithBricks($object, $line, $products): Collection
    {
        $sumSalesUnits = 0;
        $sumSalesValues = 0;
        $sumTargetUnits = 0;
        $sumTargetValues = 0;
        $sumAchievementUnits = 0;
        $sumAchievementValues = 0;
        $belowDivisions = [];
        $objectBricks = collect([]);
        if ($this->perDivOrUserFilter == 1) {
            $belowDivisions = $object->getBelowDivisions()->where('division_type_id', '=', $this->divisionType)->unique('id')->pluck('id')->toArray();
            $objectBricks = $object->bricks()->select('bricks.id', 'bricks.name')->get();
        } else {
            $belowDivisions = $object->allBelowDivisions($line)->where('division_type_id', '=', $this->divisionType)->unique('id')->pluck('id')->toArray();
        }

        $results = collect([]);
        // throw new CrmException($objectBricks);

        foreach ($objectBricks as $objectBrick) {
            $sales = $this->sales($belowDivisions, $products, $line, $objectBrick);
            $targets = $this->targets($belowDivisions, $products);
            $data = $products->map(function ($product)
            use (
                $line,
                $object,
                $belowDivisions,
                $objectBrick,
                $sales,
                $targets,
                &$sumSalesUnits,
                &$sumSalesValues,
                &$sumTargetUnits,
                &$sumTargetValues,
                &$sumAchievementUnits,
                &$sumAchievementValues,
            ) {

                [$sale, $target] = $this->getProductTargetsSales($sales, $targets, $product);
                $achievementUnits = $target['targetUnit'] ? (round($sale['salesUnit'] / $target['targetUnit'] * 100, 2)) : 0;
                $achievementValues = $target['targetValue'] ? (round($sale['salesValue'] / $target['targetValue'] * 100, 2)) : 0;
                $sumSalesUnits += $sale['salesUnit'];
                $sumSalesValues += $sale['salesValue'];
                $sumTargetUnits = $target['targetUnit'];
                $sumTargetValues = $target['targetValue'];
                $sumAchievementUnits += $achievementUnits;
                $sumAchievementValues += $achievementValues;
                return [
                    'id' => $object->id,
                    'line' => $line->name,
                    'division' => $this->perDivOrUserFilter == 1 ? $object?->name : $object->divisions()->where('line_divisions.line_id', $line->id)->pluck('name')->implode(','),
                    'brick' => $objectBrick?->name,
                    'employee' => $this->perDivOrUserFilter == 1 ? $object->users()?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(',') : $object?->fullname,
                    'product' => $product->name,
                    'brand' => '',
                    'sales_unit' => $sale['salesUnit'],
                    'sales_value' => number_format($sale['salesValue']),
                    'target_unit' => 0,
                    'target_value' => 0,
                    'achievement_unit' => $achievementUnits . '%',
                    'achievement_value' => number_format($achievementValues) . '%',
                    'color' => $this->perDivOrUserFilter == 1 ? $object?->DivisionType->color : $object->division($line)?->DivisionType?->color,
                ];
            });
            $results = $results->merge(collect($data));
        }
        // throw new CrmException($results);
        $total = collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $this->perDivOrUserFilter == 1 ? $object?->name : $object->divisions()->where('line_divisions.line_id', $line->id)->pluck('name')->implode(','),
            'brick' => '',
            'employee' => $this->perDivOrUserFilter == 1 ? $object->users()?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(',') : $object?->fullname,
            'product' => 'Total',
            'sales_unit' => $sumSalesUnits,
            'sales_value' => $sumSalesValues ? number_format(round($sumSalesValues, 2)) : 0,
            'target_unit' => $sumTargetUnits,
            'target_value' => $sumTargetValues ? number_format(round($sumTargetValues, 2)) : 0,
            'achievement_unit' => $sumTargetUnits ? (round($sumSalesUnits / $sumTargetUnits * 100, 2)) . '%' : 0 . '%',
            'achievement_value' => $sumTargetValues ? number_format((round($sumSalesValues / $sumTargetValues * 100, 2))) . '%' : 0 . '%',
            'color' => $this->perDivOrUserFilter == 1 ? $object?->DivisionType->color : $object->division($line)?->DivisionType?->color,

        ]);
        $results = $results->push($total);
        return collect($results);
    }


    private function getBelowDivisions($object, $line)
    {
        $cacheKey = "below_divisions_{$this->perDivOrUserFilter}_{$object->id}_{$line->id}_{$this->divisionType}";
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($object, $line) {
            if ($this->perDivOrUserFilter == 1) {
                return $object->getBelowDivisions($this->from, $this->to)->where('division_type_id', '=', $this->divisionType)->unique('id')->pluck('id')->toArray();
            } else {
                return $object->allBelowDivisions($line)->where('division_type_id', '=', $this->divisionType)->unique('id')->pluck('id')->toArray();
            }
        });
    }

    private function getProductDataProcessingInSalesView($products, $line, $object, $belowDivisions): Collection
    {
        $sumSalesUnits = 0;
        $sumSalesValues = 0;
        $sumTargetUnits = 0;
        $sumTargetValues = 0;
        $sumAchievementUnits = 0;
        $sumAchievementValues = 0;
        $sales = $this->sales($belowDivisions, $products, $line);
        $targets = $this->targets($belowDivisions, $products);

        $data = $products->map(function ($product)
        use (

            $line,
            $object,
            $belowDivisions,
            $sales,
            $targets,
            &$sumSalesUnits,
            &$sumSalesValues,
            &$sumTargetUnits,
            &$sumTargetValues,
            &$sumAchievementUnits,
            &$sumAchievementValues,
        ) {

            [$sale, $target] = $this->getProductTargetsSales($sales, $targets, $product);

            $achievementUnits = $target['targetUnit'] ? (round($sale['salesUnit'] / $target['targetUnit'] * 100, 2)) : 0;
            $achievementValues = $target['targetValue'] ? (round($sale['salesValue'] / $target['targetValue'] * 100, 2)) : 0;
            $sumSalesUnits += $sale['salesUnit'];
            $sumSalesValues += $sale['salesValue'];
            $sumTargetUnits += $target['targetUnit'];
            $sumTargetValues += $target['targetValue'];
            $sumAchievementUnits += $achievementUnits;
            $sumAchievementValues += $achievementValues;
            return [
                'id' => $object->id,
                'line' => $line->name,
                'division' => $object?->name,
                'employee' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(','),
                'emp_code' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('emp_code')->implode(','),
                'product' => $product->name,
                'brand' => $product->brands()?->first()?->name ?? '',
                'sales_unit' => $sale['salesUnit'],
                'sales_value' => number_format($sale['salesValue']),
                'target_unit' => $target['targetUnit'],
                'target_value' => number_format($target['targetValue']),
                'achievement_unit' => $achievementUnits . '%',
                'achievement_value' => number_format($achievementValues) . '%',
                'color' => $object?->DivisionType->color,
            ];
        });
        $total = collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $object?->name,
            'employee' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(','),
            'emp_code' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('emp_code')->implode(','),
            'product' => 'Total',
            'brand' => '',
            'sales_unit' => $sumSalesUnits,
            'sales_value' => $sumSalesValues ? number_format(round($sumSalesValues, 2)) : 0,
            'target_unit' => $sumTargetUnits,
            'target_value' => $sumTargetValues ? number_format(round($sumTargetValues, 2)) : 0,
            'achievement_unit' => $sumTargetUnits ? (round($sumSalesUnits / $sumTargetUnits * 100, 2)) . '%' : 0 . '%',
            'achievement_value' => $sumTargetValues ? number_format((round($sumSalesValues / $sumTargetValues * 100, 2))) . '%' : 0 . '%',
            'color' => $object?->DivisionType->color,
        ]);
        $data = $data->push($total);
        return collect($data);
    }

    private function getProductDataProcessing($products, $line, $object, $belowDivisions)
    {
        $months = implode(',', $this->fromMonth);
        $cacheKey = "product_data_{$this->perDivOrUserFilter}_{$object->id}_{$line->id}_{$months}_{$this->fromYear}";
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use (
            $products,
            $line,
            $object,
            $belowDivisions
        ) {

            $sales = $this->sales($belowDivisions, $products, $line);
            $targets = $this->targets($belowDivisions, $products);

            return $products->map(function ($product) use (
                $object,
                $line,
                $sales,
                $targets
            ) {
                $data = $this->initializeProductData($object, $line, $product);

                $sumSalesUnits = 0;
                $sumSalesValues = 0;
                $sumTargetUnits = 0;
                $sumTargetValues = 0;


                foreach ($this->period as $date) {

                    [$sale, $target] = $this->getProductTargetsSales($sales, $targets, $product, $date);

                    $this->updateProductDataForDate($data, $date, $sale, $target, $object, $line);

                    $sumSalesUnits += $sale['salesUnit'];
                    $sumSalesValues += $sale['salesValue'];
                    $sumTargetUnits += $target['targetUnit'];
                    $sumTargetValues += $target['targetValue'];
                }

                $this->updateProductTotals($data, $sumSalesUnits, $sumSalesValues, $sumTargetUnits, $sumTargetValues);

                return $data;
            });
        });
    }

    private function initializeProductData($object, $line, $product): Collection
    {

        return collect([
            'id' => $object->id,
            'line' => $line->name,
            'line_id' => $line->id,
            "product_id" => $product->id,
            'division' => $object?->name,
            'employee' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(','),
            'emp_code' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('emp_code')->implode(','),
            'product' => $product->name,
            'brand' => $product->brands()?->first()?->name ?? '',

            //            "division_id" => $perDivOrUserFilter == 1 ? $object?->id : $object->divisions()->where('line_divisions.line_id', $line->id)->pluck('id'),
            //            'division' => $perDivOrUserFilter == 1 ? $object?->name : $object->divisions()->where('line_divisions.line_id', $line->id)->pluck('name')->implode(','),
            //            'employee' => $perDivOrUserFilter == 1 ? $object->users()?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(',') : $object?->fullname,
            //            'product' => $product->name,
            //            'brand' => $product->brands()?->first()?->name ?? '',
        ]);
    }

    private function updateProductDataForDate(&$data, $date, $sales, $target, $object, $line): void
    {
        $monthAchieveUnits = $target['targetUnit'] ? (round($sales['salesUnit'] / $target['targetUnit'] * 100, 2)) : 0;
        $monthAchieveValues = $target['targetValue'] ? (round($sales['salesValue'] / $target['targetValue'] * 100, 2)) : 0;

        $first = Carbon::parse($date)->firstOfMonth();
        $end = Carbon::parse($date)->endOfMonth();

        $data = $data->put($date->format('M') . ' emp', $this->perDivOrUserFilter == 1 ? $object->users($first, $end)->first()?->fullname : '');
        $data = $data->put($date->format('M') . ' SU', $sales['salesUnit']);
        $data = $data->put($date->format('M') . ' SV', $sales['salesValue']);
        $data = $data->put($date->format('M') . ' TU', $target['targetUnit']);
        $data = $data->put($date->format('M') . ' TV', $target['targetValue']);
        $data = $data->put($date->format('M') . ' AU', $monthAchieveUnits . '%');
        $data = $data->put($date->format('M') . ' AV', number_format($monthAchieveValues) . '%');
    }

    private function updateProductTotals(&$data, $sumSalesUnits, $sumSalesValues, $sumTargetUnits, $sumTargetValues): void
    {
        $achievementUnits = $sumTargetUnits ? (round($sumSalesUnits / $sumTargetUnits * 100, 2)) : 0;
        $achievementValues = $sumTargetValues ? (round($sumSalesValues / $sumTargetValues * 100, 2)) . '%' : '0%';

        $data = $data->put('Tot SU', $sumSalesUnits);
        $data = $data->put('Tot SV', $sumSalesValues);
        $data = $data->put('Tot TU', $sumTargetUnits);
        $data = $data->put('Tot TV', $sumTargetValues);
        $data = $data->put('Ach U', $achievementUnits . '%');
        $data = $data->put('Ach V', $achievementValues);
    }

    private function calculateTotal($result, $object, $line): Collection
    {
        $total = $this->initializeTotalData($object, $line);

        $totalSalesUnits = 0;
        $totalSalesValues = 0;
        $totalTargetUnits = 0;
        $totalTargetValues = 0;

        foreach ($this->period as $date) {
            $this->updateTotalForDate($total, $result, $date, $totalSalesUnits, $totalSalesValues, $totalTargetUnits, $totalTargetValues);
        }

        $this->updateTotalTotals($total, $totalSalesUnits, $totalSalesValues, $totalTargetUnits, $totalTargetValues);

        return $total;
    }

    private function initializeTotalData($object, $line): Collection
    {
        return collect([
            'id' => $object->id,
            'line' => $line->name,
            'division' => $object?->name,
            'employee' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('fullname')->implode(','),
            'emp_code' => $object->users($this->from, $this->to)?->where('line_users_divisions.line_id', $line->id)->pluck('emp_code')->implode(','),
            'product' => 'Total',
            'brand' => '',
            'color' => $object?->DivisionType->color,
        ]);
    }

    private function updateTotalForDate(&$total, $result, $date, &$totalSalesUnits, &$totalSalesValues, &$totalTargetUnits, &$totalTargetValues): void
    {
        $saleU = $result->sum($date->format('M') . ' SU');
        $saleV = $result->sum($date->format('M') . ' SV');
        $targetU = $result->sum($date->format('M') . ' TU');
        $targetV = $result->sum($date->format('M') . ' TV');

        $totalSalesUnits += $saleU;
        $totalSalesValues += $saleV;
        $totalTargetUnits += $targetU;
        $totalTargetValues += $targetV;

        $achPerDateU = $targetU ? (round($saleU / $targetU * 100, 2)) : 0;
        $achPerDateV = $targetV ? (round($saleV / $targetV * 100, 2)) : 0;

        $total = $total->put($date->format('M') . ' emp', '');
        $total = $total->put($date->format('M') . ' SU', $saleU);
        $total = $total->put($date->format('M') . ' SV', round($saleV, 2));
        $total = $total->put($date->format('M') . ' TU', $targetU);
        $total = $total->put($date->format('M') . ' TV', round($targetV, 2));
        $total = $total->put($date->format('M') . ' AU', $achPerDateU . '%');
        $total = $total->put($date->format('M') . ' AV', $achPerDateV . '%');
    }

    private function updateTotalTotals(&$total, $totalSalesUnits, $totalSalesValues, $totalTargetUnits, $totalTargetValues): void
    {
        $achievementUnitsPerUser = $totalTargetUnits ? (round($totalSalesUnits / $totalTargetUnits * 100, 2)) : 0;
        $achievementValuesPerUser = $totalTargetValues ? (round($totalSalesValues / $totalTargetValues * 100, 2)) . '%' : '0%';

        $total = $total->put('Tot SU', $totalSalesUnits);
        $total = $total->put('Tot SV', $totalSalesValues);
        $total = $total->put('Tot TU', $totalTargetUnits);
        $total = $total->put('Tot TV', $totalTargetValues);
        $total = $total->put('Ach U', $achievementUnitsPerUser . '%');
        $total = $total->put('Ach V', $achievementValuesPerUser);
    }
}
