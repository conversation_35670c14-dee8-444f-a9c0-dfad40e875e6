<?php

namespace App;

use App\Exceptions\CrmException;
use App\Traits\ModelAvailability;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

class Productprice extends Model
{
    use SoftDeletes;
    use ModelAvailability;

    protected $guard_name = 'api';

    protected $table = 'product_prices';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'product_id',
        'distributor_id',
        'from_date',
        'to_date',
        'selling_price',
        'avg_price',
        'avg_tender_price',
        'avg_target_price',
        'file_id'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function distributor()
    {
        return $this->belongsTo(Distributor::class);
    }

    public static function price(Product $product, Distributor $distributor = null, Carbon $from = null, Carbon $to = null)
    {
        return self::where('product_id', $product?->id)
            ->where('from_date', '<=', Carbon::parse($from)->toDateString() ?? now()->toDateString())
            ->where(fn($q) => $q->where('to_date', '>=', Carbon::parse($to)->toDateString() ?? (string)Carbon::now())
                ->orWhere('to_date', null))
            ->where(fn($q) => $q->where('distributor_id', $distributor?->id)->orWhere('distributor_id', null))->first()?->avg_price;
    }

    public function scopeValid($query, $date = null)
    {
        $date = Carbon::parse($date) ?? Carbon::now();

        return $query->where('from_date', '<=', $date->toDateString())
            ->where(
                fn($q) => $q->where('to_date', '>=', $date->toDateString())
                    ->orWhere('to_date', null));
    }
}
